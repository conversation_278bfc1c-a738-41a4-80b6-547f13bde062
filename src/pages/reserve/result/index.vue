<route lang="json5" type="page">
{
  layout: 'default',
  style: {
    navigationBarTitleText: '审核结果页',
  },
}
</route>

<template>
  <view class="flex flex-col min-h-100vh font-['PingFang_SC']">
    <!-- 成功信息卡片 -->
    <view class="flex flex-col items-center w-full bg-white mb-3">
      <OSSImg
        src="/images/reserve/result-logo.png"
        width="108"
        height="108"
        className="mt-110rpx mb-22rpx"
      />
      <view class="text-36rpx font-500 leading-44rpx text-#111 text-center mb-8rpx">预约成功</view>
      <view class="text-28rpx leading-42rpx text-#666 text-center">
        请在预约入场时间前进入停车场
      </view>
      <view class="text-28rpx leading-42rpx text-#666 text-center">
        否则可能造成爽约，影响您后续预约
      </view>

      <!-- 预约信息列表 -->
      <view class="w-full mt-60rpx mb-60rpx">
        <view class="flex justify-between items-center px-40rpx py-12rpx">
          <view class="text-28rpx leading-40rpx text-#999">停车场</view>
          <view class="text-28rpx leading-40rpx text-#111 text-right">
            {{ parkName || '获取中...' }}
          </view>
        </view>
        <view class="flex justify-between items-center px-40rpx py-12rpx">
          <view class="text-28rpx leading-40rpx text-#999">车牌号</view>
          <view class="text-28rpx leading-40rpx text-#111 text-right">
            {{ plateLabel || plateNo || '获取中...' }}
          </view>
        </view>
        <view class="flex justify-between items-center px-40rpx py-12rpx">
          <view class="text-28rpx leading-40rpx text-#999">预约入场时间</view>
          <view class="text-28rpx leading-40rpx text-#111 text-right">
            {{ entryTimeLabel || '获取中...' }}
          </view>
        </view>
      </view>
    </view>

    <!-- 服务选项卡片 -->
    <view class="w-full flex flex-col bg-white mb-160rpx pb-6">
      <view class="text-28rpx leading-40rpx text-#111 py-24rpx text-center">
        您可能需要以下服务
      </view>

      <!-- 服务项 - 地图导航 -->
      <view class="flex justify-center w-full my-10rpx" @click="handleNavigation">
        <view class="flex items-center justify-center">
          <view class="text-28rpx leading-40rpx text-#56be66">地图导航</view>
          <view class="ml-2 relative">
            <OSSImg src="/images/reserve/arrow.png" width="24" height="24" />
          </view>
        </view>
      </view>

      <!-- 服务项 - 预约车停车指引 -->
      <view class="flex justify-center w-full my-10rpx" @click="handleGuidance">
        <view class="flex items-center justify-center">
          <view class="text-28rpx leading-40rpx text-#56be66">预约车停车指引</view>
          <view class="ml-2 relative">
            <OSSImg src="/images/reserve/arrow.png" width="24" height="24" />
          </view>
        </view>
      </view>
    </view>

    <!-- 底部按钮 -->
    <view class="w-full p-6 pb-safe-6 box-border bg-white fixed bottom-0 left-0 z-9">
      <AppButton type="brand" color="#56BE66" className="w-full h-25" @click="goHome">
        返回首页
      </AppButton>
    </view>
  </view>
</template>

<script lang="ts" setup>
import { ref } from 'vue';
import OSSImg from '@/components/OSSImg/index.vue';
import AppButton from '@/components/AppButton/index.vue';
import { BaseParkDetailVO, ParkingLotInquiryService } from '@/parkService';
import { DEFAULTGEO } from '@/config';
import { getLocation } from '@/utils/jsapi';

// 页面参数
const parkId = ref('');
const parkName = ref('');
const plateNo = ref('');
const plateLabel = ref('');
const entryTime = ref(0);
const entryTimeLabel = ref('');
const address = ref('');
const latitude = ref('');
const longitude = ref('');

// 停车场详情
const parkDetail = ref<BaseParkDetailVO>({});
const userLocation = ref({
  latitude: DEFAULTGEO.latitude,
  longitude: DEFAULTGEO.longitude,
});

// 页面加载时获取参数
onLoad((options: any) => {
  parkId.value = options.parkId || '';
  parkName.value = decodeURIComponent(options.parkName || '');
  plateNo.value = options.plateNo || '';
  plateLabel.value = decodeURIComponent(options.plateLabel || '');
  entryTime.value = Number(options.entryTime) || 0;
  entryTimeLabel.value = decodeURIComponent(options.entryTimeLabel || '');
  address.value = decodeURIComponent(options.address || '');
  latitude.value = options.latitude || '';
  longitude.value = options.longitude || '';

  // 如果没有从参数获取到停车场信息，则重新获取
  if (!parkName.value && parkId.value) {
    getStationDetail();
  }
});

// 获取用户位置
const getUserLocation = async () => {
  try {
    const { longitude: lng, latitude: lat } = await getLocation();
    userLocation.value = {
      longitude: lng,
      latitude: lat,
    };
  } catch (error) {
    console.error('获取位置失败:', error);
  }
};

// 获取停车场详情
const getStationDetail = async () => {
  await getUserLocation();

  const [, res] = await ParkingLotInquiryService.postParkDetail({
    parkId: parkId.value,
    longitude: userLocation.value.longitude,
    latitude: userLocation.value.latitude,
  });

  if (res?.data) {
    parkDetail.value = res.data;
    // 更新页面显示的信息
    if (!parkName.value) parkName.value = res.data.parkName || '';
    if (!address.value) address.value = res.data.address || '';
    if (!latitude.value) latitude.value = res.data.latitude || '';
    if (!longitude.value) longitude.value = res.data.longitude || '';
  }
};

// 返回首页
const goHome = () => {
  uni.switchTab({
    url: '/pages/home/<USER>',
  });
};

// 处理地图导航
const handleNavigation = () => {
  if (!latitude.value || !longitude.value) {
    uni.showToast({
      title: '无法获取停车场位置信息',
      icon: 'none',
    });
    return;
  }

  // 使用系统地图导航
  uni.openLocation({
    latitude: parseFloat(latitude.value),
    longitude: parseFloat(longitude.value),
    name: parkName.value,
    address: address.value,
    success: () => {
      console.log('导航成功');
    },
    fail: (err) => {
      console.error('导航失败:', err);
      uni.showToast({
        title: '导航失败',
        icon: 'none',
      });
    },
  });
};

// 处理停车指引
const handleGuidance = () => {
  // 检查是否有停车指引信息
  const guidance = parkDetail.value.reserveConfig?.parkingGuidance;

  if (guidance) {
    // 显示停车指引弹窗
    uni.showModal({
      title: '预约车停车指引',
      content: guidance,
      showCancel: false,
      confirmText: '知道了',
    });
  } else {
    uni.showToast({
      title: '暂无停车指引信息',
      icon: 'none',
    });
  }
};
</script>
